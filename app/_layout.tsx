import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Stack } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import React, { useEffect, useState } from "react";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { StatusBar } from "expo-status-bar";
import { Colors } from "@/constants/colors";
import { GardenProvider } from "@/hooks/useGardenStore";
import { FavoritesProvider } from "@/hooks/useFavoritesStore";
import { IdentificationProvider } from "@/hooks/useIdentificationStore";
import { AuthProvider, useAuth } from "@/hooks/useAuth";
import { setupGlobalErrorFiltering } from "@/utils/errorFilter";
import SplashScreenComponent from "./splash";
import LoginScreen from "./auth/login";

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

const queryClient = new QueryClient();

function AppContent() {
  const { user, loading } = useAuth();
  const [showSplash, setShowSplash] = useState(true);

  useEffect(() => {
    if (!loading) {
      // console.log('Auth loading complete, user:', user ? 'authenticated' : 'not authenticated');
      // Show splash for a minimum time even if auth loads quickly
      const timer = setTimeout(() => {
        setShowSplash(false);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [loading, user]);

  if (showSplash || loading) {
    return <SplashScreenComponent onFinish={() => setShowSplash(false)} />;
  }

  if (!user) {
    // console.log('Showing login screen - no authenticated user');
    return <LoginScreen onSuccess={() => {/* console.log('Login success callback triggered') */}} />;
  }

  console.log('Showing main app - user authenticated');

  return (
    <Stack
      screenOptions={{
        headerBackTitle: "Back",
        headerStyle: {
          backgroundColor: Colors.background,
        },
        headerTitleStyle: {
          color: Colors.text,
          fontWeight: '600',
        },
        headerTintColor: Colors.primary,
        contentStyle: {
          backgroundColor: Colors.background,
        },
      }}
    >
      <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
      <Stack.Screen name="profile/update" options={{ title: 'Update Profile' }} />
      <Stack.Screen name="modal" options={{ presentation: 'modal' }} />
      <Stack.Screen name="legal/terms" options={{ presentation: 'modal' }} />
      <Stack.Screen name="legal/privacy" options={{ presentation: 'modal' }} />
    </Stack>
  );
}

import * as Linking from "expo-linking";
import { supabase } from "@/lib/supabase";

export default function RootLayout() {
  useEffect(() => {
    SplashScreen.hideAsync();
    // Setup global error filtering to suppress browser extension errors
    setupGlobalErrorFiltering();

    // Deep link handler for OAuth callback
    const handleDeepLink = async (event: Linking.EventType) => {
      const url = typeof event === "string" ? event : event.url;
      if (url && url.includes("auth/callback")) {
        try {
          const parsed = Linking.parse(url);
          // Extract tokens from query params
          let access_token = parsed.queryParams?.access_token;
          let refresh_token = parsed.queryParams?.refresh_token;
          // Ensure tokens are strings, not arrays
          if (Array.isArray(access_token)) access_token = access_token[0];
          if (Array.isArray(refresh_token)) refresh_token = refresh_token[0];
          if (typeof access_token === "string" && typeof refresh_token === "string") {
            const { error } = await supabase.auth.setSession({
              access_token,
              refresh_token,
            });
            if (error) {
              // Optionally show error to user
              console.error("Supabase session error:", error);
            }
          }
        } catch (err) {
          console.error("Deep link handling error:", err);
        }
      }
    };

    const subscription = Linking.addEventListener("url", handleDeepLink);

    // Also handle initial URL if app was opened via deep link
    Linking.getInitialURL().then((url) => {
      if (url) handleDeepLink({ url });
    });

    return () => {
      subscription.remove();
    };
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <GardenProvider>
          <FavoritesProvider>
            <IdentificationProvider>
              <GestureHandlerRootView style={{ flex: 1 }}>
                <StatusBar style="dark" />
                <AppContent />
              </GestureHandlerRootView>
            </IdentificationProvider>
          </FavoritesProvider>
        </GardenProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
}
